// ChatAdvisor Admin Frontend PM2 配置
// 专用于前端服务的独立部署和管理
// 优化版本：统一配置模式，修复日志权限问题，增强健康检查

const path = require('path');

module.exports = {
    apps: [
        {
            name: 'admin-frontend-release',
            script: 'npx',
            args: 'vite preview --port 54001 --host 0.0.0.0',
            cwd: process.cwd(),
            watch: false,
            autorestart: true,
            max_restarts: 10,
            min_uptime: '10s',
            restart_delay: 1000,
            env: {
                NODE_ENV: 'production',
                PORT: 54001,
                HOST: '0.0.0.0',
                PM2_SERVE_PATH: './dist.current',
                PM2_SERVE_PORT: 54001,
                PM2_SERVE_SPA: 'true'
            },
            // 统一日志配置
            log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
            log_file: path.join(process.cwd(), 'logs/admin-release-combined.log'),
            error_file: path.join(process.cwd(), 'logs/admin-release-error.log'),
            out_file: path.join(process.cwd(), 'logs/admin-release-out.log'),
            merge_logs: true,
            log_type: 'json',
            // 进程配置
            instances: 1,
            exec_mode: 'fork',
            // 增强健康检查配置
            health_check_url: 'http://localhost:54001',
            health_check_grace_period: 3000,
            max_memory_restart: '500M',
            // 监控配置
            monitoring: false,
            pmx: false
        },
        {
            name: 'admin-frontend-debug',
            script: 'npx',
            args: 'vite --port 34001 --host 0.0.0.0',
            cwd: process.cwd(),
            watch: false,
            autorestart: true,
            max_restarts: 10,
            min_uptime: '10s',
            restart_delay: 1000,
            env: {
                NODE_ENV: 'development',
                PORT: 34001,
                HOST: '0.0.0.0'
            },
            // 统一日志配置
            log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
            log_file: path.join(process.cwd(), 'logs/admin-debug-combined.log'),
            error_file: path.join(process.cwd(), 'logs/admin-debug-error.log'),
            out_file: path.join(process.cwd(), 'logs/admin-debug-out.log'),
            merge_logs: true,
            log_type: 'json',
            // 进程配置
            instances: 1,
            exec_mode: 'fork',
            // 增强健康检查配置
            health_check_url: 'http://localhost:34001',
            health_check_grace_period: 3000,
            max_memory_restart: '500M',
            // 监控配置
            monitoring: false,
            pmx: false
        }
    ],

    // 修复部署配置
    deploy: {
        production: {
            user: 'root',
            host: '**************',
            ref: 'origin/main',
            repo: '**********************:server/admin-frontend.git',
            path: '/opt/chatadvisor/admin-frontend',
            'pre-deploy-local': '',
            // 修复post-deploy命令，使用正确的配置文件名
            'post-deploy': 'npm install && npm run build:prod && pm2 reload pm2.config.cjs --env production',
            'pre-setup': 'mkdir -p logs'
        }
    }
};
