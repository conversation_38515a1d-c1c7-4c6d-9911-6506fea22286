# ChatAdvisor Server GitLab CI/CD 配置
# 当提交信息包含 "release:" 时自动触发后端部署
# 使用本地PM2部署，无需SSH连接
# 优化版本：增强构建缓存、错误处理和健康检查

variables:
  BACKEND_PORT: "53011"
  SERVICE_NAME: "chat-advisor-release"
  NODE_VERSION: "20"
  NPM_CACHE_DIR: ".npm"
  BUILD_TIMEOUT: "600"
  HEALTH_CHECK_TIMEOUT: "30"
  DEPLOYMENT_TIMEOUT: "300"

# 优化缓存策略
cache:
  key:
    files:
      - package-lock.json
      - yarn.lock
    prefix: backend-${CI_COMMIT_REF_SLUG}
  paths:
    - .npm/
    - node_modules/
    - dist/
  policy: pull-push

stages:
  - validate
  - build
  - deploy
  - test
  - notify

workflow:
  rules:
    - if: $CI_COMMIT_MESSAGE =~ /release:/
      when: always
    - if: $CI_PIPELINE_SOURCE == "web"
      when: always
    - when: never

validate_backend:
  stage: validate
  tags:
    - Eva
  script:
    - "echo '🔍 验证后端部署环境...'"
    - "echo '提交信息: $CI_COMMIT_MESSAGE'"
    - "echo '分支: $CI_COMMIT_REF_NAME'"
    - "echo '项目: ChatAdvisor Server'"
    - "source ~/.bashrc"
    - "nvm use 21"
    - "node --version"
    - "npm --version"
    - "echo '✅ 后端环境验证完成'"
  only:
    variables:
      - $CI_COMMIT_MESSAGE =~ /release:/

build_backend:
  stage: build
  tags:
    - Eva
  timeout: 10m
  before_script:
    - "echo '🔧 准备构建环境...'"
    - "source ~/.bashrc"
    - "nvm use 21"
    - "node --version"
    - "npm --version"
    - "echo '📁 当前目录: $(pwd)'"
    - "echo '💾 可用磁盘空间: $(df -h . | tail -1)'"
  script:
    - "echo '🏗️ 构建后端项目...'"
    - |
      set -e

      # 清理旧的构建产物
      echo "🧹 清理旧的构建产物..."
      rm -rf dist/ || true

      # 安装依赖
      echo "📦 安装依赖..."
      if [ -f "yarn.lock" ]; then
        yarn install --frozen-lockfile --cache-folder .npm
      else
        npm ci --cache .npm --prefer-offline --no-audit
      fi

      # TypeScript编译
      echo "🏗️ TypeScript编译..."
      npm run build

      # 验证构建产物
      echo "🔍 验证构建产物..."
      if [ ! -d "dist" ] || [ ! -f "dist/src/index.js" ]; then
        echo "❌ 构建产物验证失败"
        exit 1
      fi

      echo "📦 构建产物信息:"
      du -sh dist/
      ls -la dist/
      find dist/ -name "*.js" | head -10

      echo "✅ 后端构建完成"
  artifacts:
    name: "backend-$CI_COMMIT_SHORT_SHA"
    paths:
      - dist/
      - package.json
      - package-lock.json
      - yarn.lock
      - pm2.config.cjs
      - .env
      - logs/
    expire_in: 2 hours
    reports:
      junit: dist/test-results.xml
  cache:
    key:
      files:
        - package-lock.json
        - yarn.lock
      prefix: backend-${CI_COMMIT_REF_SLUG}
    paths:
      - node_modules/
      - .npm/
      - dist/
    policy: push
  only:
    variables:
      - $CI_COMMIT_MESSAGE =~ /release:/

deploy_backend:
  stage: deploy
  tags:
    - Eva
  timeout: 5m
  dependencies:
    - build_backend
  before_script:
    - "echo '🚀 准备部署后端到生产环境...'"
    - "echo '📦 验证构建产物...'"
    - "ls -la dist/"
    - "du -sh dist/"
    - "echo '🔍 检查PM2状态...'"
    - "pm2 status || echo 'PM2未运行或无进程'"
  script:
    - "echo '🔄 部署后端构建产物...'"
    - |
      set -e

      # 创建必要的目录
      echo "📁 创建必要的目录..."
      mkdir -p logs
      chmod 755 logs

      # 备份当前版本（如果存在）
      if [ -d "dist.current" ]; then
        echo "📦 备份当前版本..."
        backup_name="dist.backup.$(date +%Y%m%d_%H%M%S)"
        mv dist.current "$backup_name"
        echo "✅ 备份完成: $backup_name"

        # 保留最近5个备份
        echo "🧹 清理旧备份..."
        ls -dt dist.backup.* 2>/dev/null | tail -n +6 | xargs rm -rf || true
      fi

      # 移动新构建产物到当前版本
      echo "📂 部署新的构建产物..."
      mv dist dist.current

      echo "🔍 验证构建产物..."
      if [ ! -d "dist.current" ] || [ ! -f "dist.current/src/index.js" ]; then
        echo "❌ 构建产物验证失败"

        # 尝试回滚到最近的备份
        latest_backup=$(ls -dt dist.backup.* 2>/dev/null | head -1 || echo "")
        if [ -n "$latest_backup" ]; then
          echo "🔄 回滚到最近备份: $latest_backup"
          mv "$latest_backup" dist.current
        fi
        exit 1
      fi

      echo "📊 构建产物信息:"
      du -sh dist.current/
      ls -la dist.current/
      echo "📁 关键文件检查:"
      ls -la dist.current/src/ | head -5

      echo "🔄 使用PM2重启后端服务..."

      # 优雅停止现有服务
      if pm2 describe $SERVICE_NAME > /dev/null 2>&1; then
        echo "🛑 优雅停止现有服务..."
        pm2 stop $SERVICE_NAME --silent
        sleep 2
        pm2 delete $SERVICE_NAME --silent || true
      else
        echo "ℹ️ 服务未运行，直接启动新服务"
      fi

      # 启动服务
      echo "🚀 启动新服务..."
      pm2 start pm2.config.cjs --only $SERVICE_NAME

      # 保存PM2配置
      pm2 save

      echo "⏳ 等待服务启动..."
      sleep 15

      echo "📊 检查PM2服务状态..."
      pm2 status $SERVICE_NAME

      # 验证服务是否正常启动
      if ! pm2 describe $SERVICE_NAME | grep -q "online"; then
        echo "❌ 服务启动失败"
        pm2 logs $SERVICE_NAME --lines 20 --nostream || true
        exit 1
      fi

      echo "✅ 后端部署完成"
  after_script:
    - "echo '📋 部署后状态检查...'"
    - "pm2 status || true"
    - "echo '💾 磁盘使用情况:'"
    - "df -h . || true"
  only:
    variables:
      - $CI_COMMIT_MESSAGE =~ /release:/

health_check_backend:
  stage: test
  tags:
    - Eva
  timeout: 30s
  dependencies:
    - deploy_backend
  script:
    - "echo '🏥 执行后端健康检查...'"
    - |
      set -e

      echo "🔍 检查PM2服务状态..."
      if ! pm2 describe $SERVICE_NAME | grep -q "online"; then
        echo "❌ PM2服务未在线"
        pm2 logs $SERVICE_NAME --lines 10 --nostream || true
        exit 1
      fi

      echo "🌐 检查后端服务 (端口 $BACKEND_PORT)..."
      health_check_passed=false

      for i in {1..15}; do
        echo "⏳ 健康检查尝试 $i/15..."

        # 检查端口是否开放
        if ! nc -z localhost $BACKEND_PORT 2>/dev/null; then
          echo "⚠️ 端口 $BACKEND_PORT 未开放"
          sleep 2
          continue
        fi

        # 检查健康检查端点
        if curl -f -s --max-time 5 "http://localhost:$BACKEND_PORT/health" > /dev/null; then
          echo "✅ 健康检查端点响应正常"
          health_check_passed=true
          break
        fi

        # 检查根路径
        if curl -f -s --max-time 5 "http://localhost:$BACKEND_PORT" > /dev/null; then
          echo "✅ 根路径响应正常"
          health_check_passed=true
          break
        fi

        echo "⏳ 等待服务完全启动... (${i}/15)"
        sleep 2
      done

      if [ "$health_check_passed" = false ]; then
        echo "❌ 后端服务健康检查失败"
        echo "🔍 诊断信息:"
        echo "PM2状态:"
        pm2 status $SERVICE_NAME || true
        echo "最近日志:"
        pm2 logs $SERVICE_NAME --lines 20 --nostream || true
        echo "端口检查:"
        netstat -tlnp | grep $BACKEND_PORT || true
        exit 1
      fi

      echo "🔍 详细API响应检查..."

      # 检查健康检查端点响应内容
      health_response=$(curl -s --max-time 5 "http://localhost:$BACKEND_PORT/health" 2>/dev/null || echo "")
      if [ -n "$health_response" ]; then
        echo "✅ 健康检查响应: $health_response"
      fi

      # 检查根路径响应
      root_response=$(curl -s --max-time 5 "http://localhost:$BACKEND_PORT" 2>/dev/null | head -c 100 || echo "")
      if [ -n "$root_response" ]; then
        echo "✅ 根路径响应正常 (前100字符): $root_response"
      fi

      echo "⏱️ 性能检查..."
      response_time=$(curl -o /dev/null -s -w "%{time_total}" --max-time 10 "http://localhost:$BACKEND_PORT/health" 2>/dev/null || echo "0")
      echo "健康检查响应时间: ${response_time}秒"

      if [ "$(echo "$response_time > 5" | bc -l 2>/dev/null || echo 0)" -eq 1 ]; then
        echo "⚠️ 响应时间较慢，可能存在性能问题"
      fi

      echo "🎉 后端健康检查完成"
  only:
    variables:
      - $CI_COMMIT_MESSAGE =~ /release:/

pm2_status_check:
  stage: test
  tags:
    - Eva
  dependencies:
    - health_check_backend
  script:
    - "echo '🔍 检查PM2服务状态...'"
    - |
      echo "📊 PM2服务状态:"
      pm2 status

      echo ""
      echo "📋 后端服务详细信息:"
      pm2 show $SERVICE_NAME || echo "⚠️ 无法获取后端服务详细信息"

      echo ""
      echo "📝 后端服务日志 (最近10行):"
      pm2 logs $SERVICE_NAME --lines 10 --nostream || echo "⚠️ 无法获取后端服务日志"
  only:
    variables:
      - $CI_COMMIT_MESSAGE =~ /release:/

notify_success:
  stage: notify
  tags:
    - Eva
  dependencies:
    - pm2_status_check
  script:
    - "echo '🎉 后端部署成功通知'"
    - |
      echo "后端部署信息:"
      echo "- 项目: ChatAdvisor Server"
      echo "- 提交: $CI_COMMIT_SHORT_SHA"
      echo "- 分支: $CI_COMMIT_REF_NAME"
      echo "- 服务: $SERVICE_NAME"
      echo "- 访问地址: http://localhost:$BACKEND_PORT"
      echo "- 健康检查: http://localhost:$BACKEND_PORT/health"
      echo "- 流水线: $CI_PIPELINE_URL"
      echo ""
      echo "PM2管理命令:"
      echo "- 查看状态: pm2 status"
      echo "- 查看日志: pm2 logs $SERVICE_NAME"
      echo "- 重启服务: pm2 restart $SERVICE_NAME"
  when: on_success
  only:
    variables:
      - $CI_COMMIT_MESSAGE =~ /release:/

notify_failure:
  stage: notify
  tags:
    - Eva
  script:
    - "echo '❌ 后端部署失败通知'"
    - |
      echo "后端部署失败信息:"
      echo "- 项目: ChatAdvisor Server"
      echo "- 提交: $CI_COMMIT_SHORT_SHA"
      echo "- 分支: $CI_COMMIT_REF_NAME"
      echo "- 流水线: $CI_PIPELINE_URL"
      echo ""
      echo "请检查:"
      echo "1. 构建日志中的错误信息"
      echo "2. PM2服务的运行状态"
      echo "3. 后端服务的日志输出"
      echo "4. 本地环境的配置状态"
  when: on_failure
  only:
    variables:
      - $CI_COMMIT_MESSAGE =~ /release:/
