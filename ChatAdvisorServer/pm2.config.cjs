// ChatAdvisor Server PM2 配置
// 专用于后端服务的独立部署和管理
// 优化版本：统一配置模式，修复日志权限问题，增强健康检查

const path = require('path');

module.exports = {
    apps: [
        {
            name: 'chat-advisor-release',
            script: 'dist.current/src/index.js',
            cwd: process.cwd(),
            watch: false,
            autorestart: true,
            max_restarts: 10,
            min_uptime: '10s',
            restart_delay: 1000,
            env: {
                NODE_ENV: 'production',
                PORT: 53011,
                PM2_GRACEFUL_TIMEOUT: 3000,
                PM2_KILL_TIMEOUT: 5000
            },
            // 统一日志配置
            log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
            log_file: path.join(process.cwd(), 'logs/release-combined.log'),
            error_file: path.join(process.cwd(), 'logs/release-error.log'),
            out_file: path.join(process.cwd(), 'logs/release-out.log'),
            merge_logs: true,
            log_type: 'json',
            // 进程配置
            instances: 1,
            exec_mode: 'fork',
            // 增强健康检查配置
            health_check_url: 'http://localhost:53011/health',
            health_check_grace_period: 3000,
            max_memory_restart: '1G',
            // 监控配置
            monitoring: false,
            pmx: false
        },
        {
            name: 'chat-advisor-debug',
            script: 'dist.current/src/index.js',
            cwd: process.cwd(),
            watch: true,
            autorestart: true,
            max_restarts: 10,
            min_uptime: '10s',
            restart_delay: 1000,
            env: {
                NODE_ENV: 'debug',
                PORT: 33001,
                PM2_GRACEFUL_TIMEOUT: 3000,
                PM2_KILL_TIMEOUT: 5000
            },
            ignore_watch: [
                'logs',
                'dist',
                'dist.current',
                'dist.backup.*',
                'config.json',
                'public/assets',
                'uploads',
                'node_modules',
                '.git',
                '*.log'
            ],
            // 统一日志配置
            log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
            log_file: path.join(process.cwd(), 'logs/debug-combined.log'),
            error_file: path.join(process.cwd(), 'logs/debug-error.log'),
            out_file: path.join(process.cwd(), 'logs/debug-out.log'),
            merge_logs: true,
            log_type: 'json',
            // 进程配置
            instances: 1,
            exec_mode: 'fork',
            // 增强健康检查配置
            health_check_url: 'http://localhost:33001/health',
            health_check_grace_period: 3000,
            max_memory_restart: '1G',
            // 监控配置
            monitoring: false,
            pmx: false
        }
    ],

    // 修复部署配置
    deploy: {
        production: {
            user: 'root',
            host: '**************',
            ref: 'origin/main',
            repo: '**********************:server/chatadvisor-server.git',
            path: '/opt/chatadvisor/ChatAdvisorServer',
            'pre-deploy-local': '',
            // 修复post-deploy命令，使用正确的配置文件名
            'post-deploy': 'npm install --production && npm run build && pm2 reload pm2.config.cjs --env production',
            'pre-setup': 'mkdir -p logs'
        }
    }
};
